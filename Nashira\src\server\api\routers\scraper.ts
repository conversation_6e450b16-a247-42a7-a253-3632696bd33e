import { z } from "zod";
import { createTR<PERSON><PERSON>outer, publicProcedure } from "~/server/api/trpc";
import { env } from "~/env";

// Flask backend configuration
const FLASK_BACKEND_URL = env.FLASK_BACKEND_URL;

// Mock function to simulate scraping data from URLs
// In a real implementation, this would make HTTP requests to the URLs
// and extract the required information using a library like cheerio
const mockScrapeUrl = (url: string) => {
  // Generate a business name from the URL
  const domain = url.replace(/^https?:\/\//, '').replace(/\/$/, '').split('/')[0];
  const domainParts = domain?.split('.') || ['example'];
  const businessName = domainParts[0] ? domainParts[0].charAt(0).toUpperCase() + domainParts[0].slice(1) : 'Business';

  // Generate a random address
  const cities = ["New York", "Los Angeles", "Chicago", "Houston", "Phoenix", "Philadelphia"];
  const randomCity = cities[Math.floor(Math.random() * cities.length)];
  const randomStreetNumber = Math.floor(Math.random() * 1000) + 1;
  const streets = ["Main St", "Broadway", "Park Ave", "Oak St", "Maple Ave", "Washington Blvd"];
  const randomStreet = streets[Math.floor(Math.random() * streets.length)];
  const address = `${randomStreetNumber} ${randomStreet}, ${randomCity}, NY`;

  // Generate random phone and email
  const phone = Math.random() > 0.2 ? `+1 (${Math.floor(Math.random() * 900) + 100}) ${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}` : '';
  const email = Math.random() > 0.3 ? `contact@${domain}` : '';

  return {
    url,
    name: businessName,
    address,
    phone,
    email
  };
};

// Parse CSV content (simple implementation)
const parseCsv = (csvContent: string): string[] => {
  // Split by newlines and filter out empty lines
  const lines = csvContent.split(/\r?\n/).filter(line => line.trim() !== '');

  // Extract URLs (assuming the first column contains URLs)
  // This is a simplified implementation - a real one would be more robust
  return lines.map(line => {
    // Handle quoted values properly
    if (line.startsWith('"')) {
      const match = line.match(/"([^"]+)"/);
      return match ? match[1] : '';
    }
    // Otherwise just take the first column
    return line.split(',')[0];
  }).filter(url => url.startsWith('http'));
};

// Define the schema for the business scraper input
const businessScrapeInputSchema = z.object({
  location: z.string().min(1),
  category: z.string().min(1), // 'category' is used as a generic search term
  country: z.string().min(1),
});

// Define the schema for the business scraper result (updated to match Flask backend)
const businessScrapeResultSchema = z.array(
  z.object({
    name: z.string(),
    address: z.string(),
    phone: z.string(),
    url: z.string(),
    category: z.string().optional(),
    social_links: z.string().optional(),
    is_shopify: z.boolean().optional(),
    is_active: z.boolean().optional(),
  })
);



// Function to call the Flask backend for business scraping
const callFlaskBackend = async (input: z.infer<typeof businessScrapeInputSchema>) => {
  try {
    console.log(`Calling Flask backend at: ${FLASK_BACKEND_URL}/api/scrape`);
    console.log(`Request data:`, input);

    const response = await fetch(`${FLASK_BACKEND_URL}/api/scrape`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        category: input.category,
        location: input.location,
        country: input.country,
        maxResults: 5, // Default value
        filterShopify: false,
        filterActive: false,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Flask backend error: ${response.status} ${response.statusText}`);
      console.error(`Error response: ${errorText}`);
      throw new Error(`Flask backend returned ${response.status}: ${errorText}`);
    }

    const results = await response.json();
    console.log(`Flask backend returned ${results.length} results`);

    return results;
  } catch (error) {
    console.error("Error calling Flask backend:", error);

    // Check if it's a network error (Flask backend not running)
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new Error("Cannot connect to Flask backend. Make sure it's running on http://localhost:5000");
    }

    throw new Error(`Failed to scrape business data: ${error instanceof Error ? error.message : String(error)}`);
  }
};

export const scraperRouter = createTRPCRouter({
  uploadCsv: publicProcedure
    .input(
      z.object({
        fileName: z.string(),
        fileContent: z.string(), // Base64 encoded file content
      })
    )
    .mutation(async ({ input }) => {
      try {
        console.log(`Processing CSV upload: ${input.fileName}`);

        // Create FormData to send to Flask backend
        const formData = new FormData();

        // Convert base64 back to file
        const buffer = Buffer.from(input.fileContent, 'base64');
        const blob = new Blob([buffer], { type: 'text/csv' });
        formData.append('file', blob, input.fileName);

        // Call Flask backend CSV upload endpoint
        const response = await fetch(`${FLASK_BACKEND_URL}/api/upload-csv-with-uploader`, {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`Flask backend CSV error: ${response.status} ${response.statusText}`);
          console.error(`Error response: ${errorText}`);
          throw new Error(`Flask backend returned ${response.status}: ${errorText}`);
        }

        const results = await response.json();
        console.log(`Flask backend returned ${results.length} CSV results`);

        return results;
      } catch (error) {
        console.error("Error processing CSV:", error);

        // Check if it's a network error (Flask backend not running)
        if (error instanceof TypeError && error.message.includes('fetch')) {
          throw new Error("Cannot connect to Flask backend. Make sure it's running on http://localhost:5000");
        }

        throw new Error("Failed to process the CSV file");
      }
    }),

  scrapeBusiness: publicProcedure
    .input(businessScrapeInputSchema)
    .mutation(async ({ input }) => {
      try {
        // Call the Flask backend for business scraping
        const results = await callFlaskBackend(input);

        // Validate the results
        return businessScrapeResultSchema.parse(results);
      } catch (error) {
        console.error("Error scraping business data:", error);
        throw new Error("Failed to scrape business data");
      }
    }),
});
