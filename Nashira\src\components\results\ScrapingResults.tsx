"use client"

import { useState, useMemo } from "react"
import { But<PERSON> } from "~/components/ui/button"
import { Card, CardContent } from "~/components/ui/card"

export interface ScrapedResult {
  name: string
  address: string
  phone: string
  url: string
  category?: string
  social_links?: string
  is_shopify: boolean
  is_active: boolean
  status?: string
}

interface ScrapingResultsProps {
  data: ScrapedResult[]
  title: string
}

type SortField = "name" | "address" | "phone" | "url" | "social_links" | "is_shopify" | "is_active"
type SortDirection = "asc" | "desc"

export default function ScrapingResults({ data, title }: ScrapingResultsProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  const [sortField, setSortField] = useState<SortField>("name")
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc")

  // Handle sorting
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
  }

  // Sort and paginate data
  const sortedData = useMemo(() => {
    return [...data].sort((a, b) => {
      let comparison = 0
      
      switch (sortField) {
        case "name":
          comparison = a.name.localeCompare(b.name)
          break
        case "address":
          comparison = a.address.localeCompare(b.address)
          break
        case "phone":
          comparison = a.phone.localeCompare(b.phone)
          break
        case "url":
          comparison = a.url.localeCompare(b.url)
          break
        case "social_links":
          comparison = (a.social_links || "N/A").localeCompare(b.social_links || "N/A")
          break
        case "is_shopify":
          comparison = (a.is_shopify === b.is_shopify) ? 0 : a.is_shopify ? -1 : 1
          break
        case "is_active":
          comparison = (a.is_active === b.is_active) ? 0 : a.is_active ? -1 : 1
          break
      }
      
      return sortDirection === "asc" ? comparison : -comparison
    })
  }, [data, sortField, sortDirection])

  // Pagination
  const totalPages = Math.ceil(sortedData.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentData = sortedData.slice(startIndex, endIndex)

  // Download as CSV
  const downloadCsv = () => {
    const headers = ["Name", "Address", "Phone", "URL", "Category", "Social Links", "Shopify Site", "Active Domain", "Status"]
    const rows = sortedData.map(item => [
      item.name,
      item.address,
      item.phone,
      item.url,
      item.category || "",
      item.social_links || "",
      item.is_shopify ? "Yes" : "No",
      item.is_active ? "Yes" : "No",
      item.status || ""
    ])
    
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
    ].join('\n')
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.setAttribute('href', url)
    link.setAttribute('download', `${title.toLowerCase().replace(/\s+/g, '_')}_results.csv`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  if (data.length === 0) {
    return (
      <Card className="bg-white/10 backdrop-blur-sm border-white/20">
        <CardContent className="p-8 text-center">
          <p className="text-white text-lg">No results found. Try adjusting your search criteria.</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-white">{title}</h2>
          <p className="text-white/70">Found {data.length} results</p>
        </div>
        <Button
          onClick={downloadCsv}
          className="bg-green-600 hover:bg-green-700 text-white"
        >
          Download CSV
        </Button>
      </div>

      {/* Results Table */}
      <Card className="bg-white/10 backdrop-blur-sm border-white/20 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full text-white">
            <thead>
              <tr className="border-b border-white/10 bg-white/10 text-left">
                <th 
                  className="cursor-pointer p-4 font-semibold"
                  onClick={() => handleSort("name")}
                >
                  Name {sortField === "name" && (sortDirection === "asc" ? "↑" : "↓")}
                </th>
                <th 
                  className="cursor-pointer p-4 font-semibold"
                  onClick={() => handleSort("address")}
                >
                  Address {sortField === "address" && (sortDirection === "asc" ? "↑" : "↓")}
                </th>
                <th 
                  className="cursor-pointer p-4 font-semibold"
                  onClick={() => handleSort("phone")}
                >
                  Phone {sortField === "phone" && (sortDirection === "asc" ? "↑" : "↓")}
                </th>
                <th 
                  className="cursor-pointer p-4 font-semibold"
                  onClick={() => handleSort("url")}
                >
                  URL {sortField === "url" && (sortDirection === "asc" ? "↑" : "↓")}
                </th>
                <th 
                  className="cursor-pointer p-4 font-semibold"
                  onClick={() => handleSort("social_links")}
                >
                  Social Links {sortField === "social_links" && (sortDirection === "asc" ? "↑" : "↓")}
                </th>
                <th 
                  className="cursor-pointer p-4 font-semibold"
                  onClick={() => handleSort("is_shopify")}
                >
                  Shopify {sortField === "is_shopify" && (sortDirection === "asc" ? "↑" : "↓")}
                </th>
                <th 
                  className="cursor-pointer p-4 font-semibold"
                  onClick={() => handleSort("is_active")}
                >
                  Active {sortField === "is_active" && (sortDirection === "asc" ? "↑" : "↓")}
                </th>
              </tr>
            </thead>
            <tbody>
              {currentData.map((item, index) => (
                <tr 
                  key={index} 
                  className="border-b border-white/10 transition hover:bg-white/5"
                >
                  <td className="p-4">{item.name}</td>
                  <td className="p-4">{item.address}</td>
                  <td className="p-4">{item.phone || "N/A"}</td>
                  <td className="p-4">
                    <a
                      href={item.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-400 hover:underline"
                    >
                      {item.url}
                    </a>
                  </td>
                  <td className="p-4">
                    {item.social_links && item.social_links !== "N/A" ? (
                      <div className="flex flex-col gap-1">
                        {item.social_links.split("; ").map((link, i) => (
                          <a
                            key={i}
                            href={link}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-400 hover:underline"
                          >
                            {link.includes("facebook.com") ? "Facebook" :
                             link.includes("twitter.com") ? "Twitter" :
                             link.includes("instagram.com") ? "Instagram" :
                             link.includes("linkedin.com") ? "LinkedIn" :
                             link.includes("youtube.com") ? "YouTube" :
                             link.includes("pinterest.com") ? "Pinterest" :
                             link.includes("tiktok.com") ? "TikTok" :
                             link.includes("x.com") ? "X" :
                             new URL(link).hostname}
                          </a>
                        ))}
                      </div>
                    ) : (
                      "N/A"
                    )}
                  </td>
                  <td className="p-4">
                    <span className={item.is_shopify ? "text-green-400" : "text-white/50"}>
                      {item.is_shopify ? "Yes" : "No"}
                    </span>
                  </td>
                  <td className="p-4">
                    <span className={item.is_active ? "text-green-400" : "text-white/50"}>
                      {item.is_active ? "Yes" : "No"}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center gap-2">
          <Button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            variant="outline"
            className="text-white border-white/20"
          >
            Previous
          </Button>
          
          <span className="text-white px-4">
            Page {currentPage} of {totalPages}
          </span>
          
          <Button
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            variant="outline"
            className="text-white border-white/20"
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
}
